<template>
    <div class="p-6">
        <!-- Upload Header -->
        <div class="flex items-center justify-between mb-6" v-if="mediaItem.status !== 'completed'">
            <div class="flex items-center gap-3">
                <div class="w-12 h-12 rounded-full flex items-center justify-center"
                     :class="getStatusColor(mediaItem.status)">
                    <VsxIcon :iconName="getStatusIcon(mediaItem.status)" :size="24" type="linear" />
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-900">{{ getStatusText(mediaItem.status) }}</p>
                    <p class="text-xs text-gray-500">{{ mediaItem.type === 'image' ? 'Image' : 'Video' }} Upload</p>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center gap-2">
                <button
                    v-if="mediaItem.status === 'pending' || mediaItem.status === 'processing'"
                    @click="$emit('cancel', mediaItem.id)"
                    class="p-2 text-gray-400 hover:text-red-600 transition-colors rounded-lg hover:bg-red-50"
                    title="Cancel upload"
                >
                    <VsxIcon iconName="CloseCircle" :size="16" type="linear" />
                </button>
            </div>
        </div>

        <!-- Enhanced Loading State with Upload Progress -->
        <div v-if="mediaItem.status === 'pending' || mediaItem.status === 'processing'" class="space-y-6">
            <!-- Progress Bar with Upload Progress -->
            <div class="flex items-center gap-4">
                <div class="flex-1 bg-gray-100 rounded-full h-2">
                    <div
                        class="h-2 rounded-full transition-all duration-500 ease-out"
                        :class="getProgressBarColor(mediaItem.status)"
                        :style="{ width: getProgressWidth() }"
                    ></div>
                </div>
                <span class="text-xs text-gray-500 min-w-[60px] font-medium">
                    {{ getProgressPercentage() }}%
                </span>
            </div>

            <!-- Upload Info -->
            <div class="flex items-center gap-3 text-sm text-gray-600">
                <VsxIcon iconName="DocumentUpload" :size="16" type="linear" />
                <span>{{ mediaItem.fileName }} ({{ mediaItem.fileSizeMB }}MB)</span>
            </div>

            <!-- Modern Grid Skeleton Loading for Images -->
            <div v-if="mediaItem.type === 'image'" class="image-gallery-grid">
                <div class="skeleton-tile">
                    <div class="skeleton-content">
                        <div class="skeleton-spinner"></div>
                    </div>
                </div>
            </div>

            <!-- Sophisticated Skeleton Loading for Videos -->
            <div v-else-if="mediaItem.type === 'video'" class="space-y-4">
                <div class="relative w-full aspect-video bg-gradient-to-r from-gray-100 via-gray-50 to-gray-100 rounded-xl animate-shimmer"
                     style="background-size: 200% 100%;">
                    <div class="absolute inset-0 flex items-center justify-center">
                        <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                            <div class="w-6 h-6 border-l-4 border-white/40 rounded-full animate-spin"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Uploaded Media Display -->
        <div v-if="mediaItem.status === 'completed' && mediaItem.result">
            <!-- Image Display -->
            <div v-if="mediaItem.type === 'image'">
                <!-- Image Count Display -->
                <div v-if="processedImages.length > 0" class="mb-6">
                    <span class="text-sm font-medium text-gray-700">
                        {{ processedImages.length }} image{{ processedImages.length > 1 ? 's' : '' }} uploaded
                    </span>
                </div>

                <!-- Modern Clean Grid Layout -->
                <div v-if="processedImages.length > 0" class="image-gallery-grid">
                    <div
                        v-for="(image, index) in processedImages"
                        :key="`uploaded-${index}`"
                        class="image-tile"
                        @click="handleImageClick(index)"
                    >
                        <img
                            :src="image.url"
                            :alt="image.alt"
                            class="image-tile-img"
                            @load="handleImageLoad"
                            @error="handleImageError"
                        />
                        <!-- Loading indicator for each image -->
                        <div v-if="!imageLoadStates[index]" class="absolute inset-0 bg-gray-100 flex items-center justify-center">
                            <div class="skeleton-spinner"></div>
                        </div>

                        <!-- Upload Badge -->
                        <div class="absolute top-2 right-2 bg-white/90 backdrop-blur-sm rounded-full p-1.5 shadow-sm">
                            <VsxIcon iconName="DocumentUpload" :size="12" type="linear" class="text-gray-600" />
                        </div>

                        <div class="image-tile-overlay"></div>
                    </div>
                </div>

                <!-- No Images Found Message -->
                <div v-else class="text-center py-8 text-gray-500">
                    <VsxIcon iconName="Gallery" :size="48" class="mx-auto mb-4 text-gray-300" />
                    <p>No images found in the upload result</p>
                </div>
            </div>

            <!-- Enhanced Video Display -->
            <div v-else-if="mediaItem.type === 'video'" class="space-y-6">
                <div v-for="(video, index) in getVideoResults(mediaItem.result)" :key="index" class="relative">
                    <video
                        :src="video.url"
                        controls
                        class="w-full aspect-video rounded-xl shadow-sm cursor-pointer"
                        @loadeddata="$emit('media-loaded')"
                        @error="handleVideoError"
                        @click="handleVideoClick(video, index)"
                    >
                        Your browser does not support the video tag.
                    </video>

                    <!-- Upload Badge for videos -->
                    <div class="absolute top-2 right-2 bg-white/90 backdrop-blur-sm rounded-full p-1.5 shadow-sm">
                        <VsxIcon iconName="DocumentUpload" :size="12" type="linear" class="text-gray-600" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Error Display with Retry Functionality -->
        <div v-else-if="mediaItem.status === 'failed'" class="space-y-4">
            <div class="p-6 bg-red-50 border border-red-100 rounded-xl">
                <div class="flex items-start gap-4">
                    <div class="flex-shrink-0">
                        <VsxIcon iconName="CloseCircle" :size="32" type="linear" class="text-red-500" />
                    </div>
                    <div class="flex-1">
                        <h3 class="text-sm font-semibold text-red-800 mb-1">Upload Failed</h3>
                        <p class="text-sm text-red-700 mb-4">
                            {{ getErrorMessage(mediaItem.error) }}
                        </p>
                        <div class="flex items-center gap-3">
                            <button
                                @click="handleRetry"
                                :disabled="isRetrying"
                                class="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            >
                                <VsxIcon v-if="isRetrying" iconName="Refresh" :size="16" type="linear" class="animate-spin mr-2" />
                                {{ isRetrying ? 'Retrying...' : 'Try Again' }}
                            </button>
                            <button
                                @click="$emit('cancel', mediaItem.id)"
                                class="px-4 py-2 bg-gray-200 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-300 transition-colors"
                            >
                                Remove
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Image Modal Integration -->
        <ImageModal
            v-if="imageModal.isOpen.value"
            :images="processedImages"
            :currentIndex="imageModal.currentIndex.value"
            @close="imageModal.closeModal"
            @previous="imageModal.previousImage"
            @next="imageModal.nextImage"
            @image-load="handleModalImageLoad"
        />
    </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { VsxIcon } from 'vue-iconsax'
import ImageModal from '@/Components/Common/ImageModal.vue'
import { useImageModal } from '@/composables/useImageModal.js'

const props = defineProps({
    mediaItem: {
        type: Object,
        required: true
    }
})

const emit = defineEmits(['cancel', 'media-loaded', 'retry'])

// Reactive state
const isRetrying = ref(false)
const imageLoadStates = ref({})

// Use the image modal composable
const imageModal = useImageModal()

// Computed property to process uploaded images
const processedImages = computed(() => {
    if (!props.mediaItem.result) {
        return []
    }

    const images = []
    const result = props.mediaItem.result

    // Handle uploaded media structure
    if (result.output) {
        const output = result.output

        // Add primary image_url first if it exists
        if (output.image_url) {
            images.push({
                url: output.image_url,
                alt: 'Uploaded image',
                width: '1024',
                height: '1024',
                isPrimary: true
            })
        }

        // Add all temporary image variations
        if (output.temporary_image_urls && Array.isArray(output.temporary_image_urls)) {
            output.temporary_image_urls.forEach((url, index) => {
                if (url && url !== output.image_url) { // Avoid duplicates
                    images.push({
                        url: url,
                        alt: `Uploaded image ${index + 1}`,
                        width: '1024',
                        height: '1024',
                        isPrimary: false
                    })
                }
            })
        }
    }

    // Fallback for direct URL structure
    if (images.length === 0 && result.url) {
        return [{
            url: result.url,
            alt: 'Uploaded media',
            width: '1024',
            height: '1024',
            isPrimary: true
        }]
    }

    return images
})

const handleImageClick = (index) => {
    imageModal.openModal(processedImages.value, index)
}

const handleVideoClick = (video, index) => {
    // For videos, we can open them in the modal as well
    const videoAsImage = [{
        url: video.url,
        alt: `Uploaded video ${index + 1}`,
        isVideo: true
    }]
    imageModal.openModal(videoAsImage, 0)
}

const handleImageLoad = (event) => {
    // Find the index of the loaded image
    const imgSrc = event.target.src
    const imageIndex = processedImages.value.findIndex(img => img.url === imgSrc)
    if (imageIndex !== -1) {
        imageLoadStates.value[imageIndex] = true
    }
    emit('media-loaded')
}

const handleModalImageLoad = () => {
    emit('media-loaded')
}

// Enhanced error handling
const handleImageError = (event) => {
    console.error('UploadedMedia: Image failed to load:', {
        src: event.target.src,
        error: event,
        mediaItem: props.mediaItem
    })
    const target = event.target
    target.style.backgroundColor = '#f3f4f6'
    target.style.display = 'flex'
    target.style.alignItems = 'center'
    target.style.justifyContent = 'center'
    target.innerHTML = '<span style="color: #9ca3af; font-size: 14px;">Image unavailable</span>'
}

const handleVideoError = (event) => {
    console.warn('UploadedMedia: Video failed to load:', event.target.src)
}

const handleRetry = async () => {
    if (isRetrying.value) return

    isRetrying.value = true
    try {
        emit('retry', props.mediaItem.id)
        // Reset retry state after a delay
        setTimeout(() => {
            isRetrying.value = false
        }, 2000)
    } catch (error) {
        console.error('Retry failed:', error)
        isRetrying.value = false
    }
}

const getErrorMessage = (error) => {
    if (!error) return 'An unexpected error occurred during upload. Please try again.'

    // Common upload error messages with user-friendly alternatives
    const errorMap = {
        'file_too_large': 'File size is too large. Please choose a smaller file.',
        'invalid_file_type': 'File type not supported. Please choose a valid image or video file.',
        'network_error': 'Network error occurred. Please check your connection and try again.',
        'server_error': 'Server error occurred. Please try again in a few moments.',
        'upload_timeout': 'Upload timed out. Please try again with a smaller file.'
    }

    // Check if error matches any known patterns
    for (const [key, message] of Object.entries(errorMap)) {
        if (error.toLowerCase().includes(key)) {
            return message
        }
    }

    return error.length > 100 ? 'Upload failed due to a technical issue. Please try again.' : error
}

// Upload-specific status helpers
const getStatusColor = (status) => {
    const colors = {
        pending: 'bg-blue-100 text-blue-700',
        processing: 'bg-blue-100 text-blue-700',
        completed: 'bg-green-100 text-green-700',
        failed: 'bg-red-100 text-red-700',
        cancelled: 'bg-gray-100 text-gray-700'
    }
    return colors[status] || colors.pending
}

const getStatusIcon = (status) => {
    const iconMap = {
        pending: 'DocumentUpload',
        processing: 'DocumentUpload',
        completed: 'TickCircle',
        failed: 'CloseCircle',
        cancelled: 'MinusCircle'
    }
    return iconMap[status] || 'DocumentUpload'
}

const getProgressBarColor = (status) => {
    const colors = {
        pending: 'bg-blue-500',
        processing: 'bg-blue-500',
        completed: 'bg-green-500',
        failed: 'bg-red-500',
        cancelled: 'bg-gray-500'
    }
    return colors[status] || colors.pending
}

const getProgressWidth = () => {
    // Use actual upload progress if available, otherwise use status-based progress
    if (props.mediaItem.uploadProgress !== undefined) {
        return `${props.mediaItem.uploadProgress}%`
    }

    const widths = {
        pending: '10%',
        processing: '50%',
        completed: '100%',
        failed: '100%',
        cancelled: '50%'
    }
    return widths[props.mediaItem.status] || '0%'
}

const getProgressPercentage = () => {
    // Use actual upload progress if available, otherwise use status-based progress
    if (props.mediaItem.uploadProgress !== undefined) {
        return Math.round(props.mediaItem.uploadProgress)
    }

    const percentages = {
        pending: 10,
        processing: 50,
        completed: 100,
        failed: 100,
        cancelled: 50
    }
    return percentages[props.mediaItem.status] || 0
}

const getStatusText = (status) => {
    const texts = {
        pending: 'Uploading',
        processing: 'Processing',
        completed: 'Uploaded',
        failed: 'Failed',
        cancelled: 'Cancelled'
    }
    return texts[status] || 'Unknown'
}

const getVideoResults = (result) => {
    if (!result) return []

    const videos = []

    // Handle uploaded video structure
    if (result.output && result.output.video_url) {
        videos.push({
            url: result.output.video_url,
            duration: result.duration || 'Unknown'
        })
    }

    // Fallback for direct URL structure
    if (videos.length === 0 && result.url) {
        videos.push({
            url: result.url,
            duration: 'Unknown'
        })
    }

    return videos
}
</script>

<style scoped>
/* Animations */
@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-shimmer {
    animation: shimmer 2s ease-in-out infinite;
}

.animate-fadeIn {
    animation: fadeIn 0.3s ease-out;
}

/* Enhanced shimmer effect */
.animate-shimmer {
    background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);
    background-size: 200% 100%;
    animation: shimmer 2s ease-in-out infinite;
}

/* Modern Image Gallery Grid Layout */
.image-gallery-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    width: 100%;
}

/* Responsive grid columns */
@media (min-width: 640px) {
    .image-gallery-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
    }
}

@media (min-width: 768px) {
    .image-gallery-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 16px;
    }
}

@media (min-width: 1024px) {
    .image-gallery-grid {
        grid-template-columns: repeat(5, 1fr);
        gap: 16px;
    }
}

@media (min-width: 1280px) {
    .image-gallery-grid {
        grid-template-columns: repeat(6, 1fr);
        gap: 20px;
    }
}

/* Image tile styling */
.image-tile {
    position: relative;
    aspect-ratio: 1 / 1;
    overflow: hidden;
    border-radius: 8px;
    cursor: pointer;
    background-color: #f8fafc;
    transition: transform 0.2s ease-out;
}

.image-tile:hover {
    transform: scale(1.02);
}

.image-tile-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    transition: opacity 0.2s ease-out;
}

.image-tile-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0);
    transition: background 0.2s ease-out;
    pointer-events: none;
}

.image-tile:hover .image-tile-overlay {
    background: rgba(0, 0, 0, 0.1);
}

.image-tile:hover .image-tile-img {
    opacity: 0.95;
}

/* Skeleton tile styling */
.skeleton-tile {
    position: relative;
    aspect-ratio: 1 / 1;
    overflow: hidden;
    border-radius: 8px;
    background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);
    background-size: 200% 100%;
    animation: shimmer 2s ease-in-out infinite;
}

.skeleton-content {
    position: absolute;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.skeleton-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid #e2e8f0;
    border-top: 2px solid #94a3b8;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Smooth transitions for all interactive elements */
.transition-all {
    transition: all 0.2s ease-in-out;
}
</style>
