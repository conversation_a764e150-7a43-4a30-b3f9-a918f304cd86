<template>
    <Teleport to="body">
        <Transition name="modal" appear>
            <div
                v-if="isOpen"
                class="fixed inset-0 z-50 flex items-center justify-center p-4"
                @click="handleBackdropClick"
                @keydown.esc="$emit('close')"
                tabindex="0"
            >
                <div class="absolute inset-0 bg-black/50 backdrop-blur-sm"></div>

                <div
                    class="relative max-w-5xl max-h-[90vh] bg-white rounded-lg shadow-xl overflow-hidden"
                    @click.stop
                >
                    <!-- Close Button -->
                    <button
                        @click="$emit('close')"
                        class="absolute top-6 right-6 z-10 w-24 h-24 bg-black/30 hover:bg-black/50 text-white rounded-full flex items-center justify-center transition-colors duration-200 backdrop-blur-sm"
                        aria-label="Close modal"
                    >
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2.5">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>

                    <!-- Navigation Buttons -->
                    <button
                        v-if="images.length > 1"
                        @click="$emit('navigate', -1)"
                        class="absolute left-6 top-1/2 transform -translate-y-1/2 z-10 w-40 h-40 bg-black/30 hover:bg-black/50 text-white rounded-full flex items-center justify-center transition-all duration-200 backdrop-blur-sm hover:scale-110"
                        aria-label="Previous image"
                    >
                        <svg class="w-20 h-20" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>

                    <button
                        v-if="images.length > 1"
                        @click="$emit('navigate', 1)"
                        class="absolute right-6 top-1/2 transform -translate-y-1/2 z-10 w-40 h-40 bg-black/30 hover:bg-black/50 text-white rounded-full flex items-center justify-center transition-all duration-200 backdrop-blur-sm hover:scale-110"
                        aria-label="Next image"
                    >
                        <svg class="w-20 h-20" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>

                    <!-- Media Display -->
                    <div class="flex items-center justify-center min-h-[200px]">
                        <!-- Video Display -->
                        <video
                            v-if="currentImage && currentImage.isVideo"
                            :src="currentImage.url"
                            controls
                            class="max-w-full max-h-[80vh] object-contain"
                            @loadeddata="$emit('image-load')"
                            @error="handleVideoError"
                        >
                            Your browser does not support the video tag.
                        </video>

                        <!-- Image Display -->
                        <img
                            v-else-if="currentImage"
                            :src="currentImage.url"
                            :alt="currentImage.alt || `Media ${currentIndex + 1}`"
                            class="max-w-full max-h-[80vh] object-contain"
                            @load="$emit('image-load')"
                            @error="handleImageError"
                        />
                    </div>

                    <!-- Image Info -->
                    <div class="p-4 bg-gray-50 border-t">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-600">
                                <span v-if="images.length > 1">
                                    {{ currentImage?.isVideo ? 'Video' : 'Media' }} {{ currentIndex + 1 }} of {{ images.length }}
                                </span>
                                <span v-else>
                                    {{ currentImage?.isVideo ? 'Video' : 'Media' }}
                                </span>
                            </div>
                            <div class="flex gap-2">
                                <a
                                    v-if="currentImage?.url"
                                    :href="currentImage.url"
                                    target="_blank"
                                        download
                                        class="p-6 text-sm bg-information-dark hover:bg-information-dark text-white rounded transition-colors duration-200"
                                >
                                    Download
                                </a>
                                <slot name="actions" :image="currentImage" :index="currentIndex">
                                </slot>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Transition>
    </Teleport>
</template>

<script setup>
import { onMounted, onUnmounted, watch } from 'vue'

const props = defineProps({
    isOpen: {
        type: Boolean,
        default: false
    },
    images: {
        type: Array,
        default: () => []
    },
    currentIndex: {
        type: Number,
        default: 0
    },
    currentImage: {
        type: Object,
        default: null
    }
})

const emit = defineEmits(['close', 'navigate', 'image-load'])

const handleBackdropClick = () => {
    emit('close')
}

const handleImageError = (event) => {
    console.error('ImageModal: Image failed to load:', event.target.src)
    const target = event.target
    target.style.backgroundColor = '#f3f4f6'
    target.style.display = 'flex'
    target.style.alignItems = 'center'
    target.style.justifyContent = 'center'
    target.innerHTML = '<span style="color: #9ca3af; font-size: 14px;">Image unavailable</span>'
}

const handleVideoError = (event) => {
    console.error('ImageModal: Video failed to load:', event.target.src)
}

const handleKeydown = (event) => {
    if (!props.isOpen) return

    switch (event.key) {
        case 'Escape':
            emit('close')
            break
        case 'ArrowLeft':
            if (props.images.length > 1) {
                emit('navigate', -1)
            }
            break
        case 'ArrowRight':
            if (props.images.length > 1) {
                emit('navigate', 1)
            }
            break
    }
}

watch(() => props.isOpen, (isOpen) => {
    if (isOpen) {
        document.body.style.overflow = 'hidden'
    } else {
        document.body.style.overflow = ''
    }
})

onMounted(() => {
    document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown)
    document.body.style.overflow = ''
})
</script>

<style scoped>
/* Modal Transitions */
.modal-enter-active, .modal-leave-active {
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.modal-enter-from, .modal-leave-to {
    opacity: 0;
    transform: scale(0.95);
}

.modal-enter-to, .modal-leave-from {
    opacity: 1;
    transform: scale(1);
}

/* Backdrop animation */
.modal-enter-active .absolute.inset-0,
.modal-leave-active .absolute.inset-0 {
    transition: opacity 0.3s ease;
}

.modal-enter-from .absolute.inset-0,
.modal-leave-to .absolute.inset-0 {
    opacity: 0;
}

/* Modal content animation */
.modal-enter-active .relative,
.modal-leave-active .relative {
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.modal-enter-from .relative,
.modal-leave-to .relative {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
}

.modal-enter-to .relative,
.modal-leave-from .relative {
    opacity: 1;
    transform: scale(1) translateY(0);
}

/* Focus styles for accessibility */
.modal-enter-to [tabindex="0"]:focus {
    outline: none;
}
</style>
